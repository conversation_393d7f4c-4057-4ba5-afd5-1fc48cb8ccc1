<?php
// Chi tiết kết quả chào giá trực tuyến rút gọn

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
require NV_ROOTDIR . '/functions/bidding_crawl.php';

$id = (int) $request_mode->get('id', 0); // Muốn bóc row nào thì nhập vào
if (!empty($id)) {
    $query_url = $dbcr->query("SELECT * FROM nv23_crawls_kqcgtt WHERE id = $id");
} else {
    $uniqid = uniqid('', true);
    $dbcr->query("UPDATE nv23_crawls_kqcgtt SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid='" . $uniqid . "', dauthau_info = 0 WHERE `url_run`=0 AND uniqid='' ORDER BY `id` DESC LIMIT 5");
    $query_url = $dbcr->query('SELECT * FROM nv23_crawls_kqcgtt WHERE uniqid=' . $dbcr->quote($uniqid));
}

$num_processed = 0;
while ($_data = $query_url->fetch()) {
    print_r('============== START ' . $_data['id'] . ': ' . $_data['notifyno'] . ' =================' . PHP_EOL);
    $detail1 = json_decode($_data['detail1'], true);
    $detail2 = geturlpage($_data, 1);

    if (empty($detail2)) {
        echo 'Lỗi API không trả về giá trị mong muốn' . PHP_EOL;
        $dbcr->query("UPDATE nv23_crawls_kqcgtt SET dauthau_info='-3', crawls_info = " . $dbcr->quote('Lỗi API không trả về giá trị detail2 mong muốn') . " WHERE id=" . $_data['id']);
        continue;
    }

    $save_detail_error = false;
    try {
        $dbcr->query("UPDATE nv23_crawls_kqcgtt SET detail2=" . $dbcr->quote(json_encode($detail2, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_data['id']);
        crawls_location:
        getdata($detail1, $detail2, $_data['id']);
    } catch (PDOException $e) {
        trigger_error($e);
        if (preg_match('/Data too long for column \'detail[23]\'/', $e->getMessage())) {
            $save_detail_error = true;
            goto crawls_location;
        }
        $dbcr->query("UPDATE `nv23_crawls_kqcgtt` SET dauthau_info='-2', crawls_info=" . $db->quote(print_r($e, true)) . " WHERE id = " . $_data['id']);
    }
    // Bóc đến đây OK rồi thì xóa file cache đi
    if (!$save_detail_error) {
        if ($file_list = glob(NV_ROOTDIR . '/crawls_2023_manual/kqcgtt_cache/kqcgtt_' . $_data['id_msc'] . '*')) {
            foreach ($file_list as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    ++$num_processed;
}
$query_url->closeCursor();
if (empty($num_processed)) {
    echo "No data\n";
}
echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function geturlpage($_row, $reload = 1)
{
    global $dbcr, $num_run_offer;
    ++$num_run_offer;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/online-reoffer/detail';
    $body = '{"id":"' . $_row['id_msc'] . '"}';
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?p_p_id=egpportalcontractorselectionv2_WAR_egpportalcontractorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorselectionv2_WAR_egpportalcontractorselectionv2_render=detail-v2&type=es-notify-contractor&id=' . $_tbmt['id'] . '&notifyId=' . $_tbmt['id'] . '&processApply=' . $_tbmt['processapply'] . '&bidMode=1_MTHS&notifyNo=' . $_tbmt['notifyno'] . '&pno=' . $_tbmt['notifyno'];
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    if (MSC_TOKEN_USE) {
        $token = get_msc_token('kqcgtt_detail');
        if ($token) {
            $url = $url . '?token=' . $token['token'];
        } else {
            // Nếu hết token thì ngưng phiên bóc
            $dbcr->exec('UPDATE nv23_crawls_kqcgtt SET url_run = 0, uniqid = "", count_url=count_url-1 WHERE url_run = "-' . NV_CURRENTTIME . '" AND dauthau_info >= 0');
            exit("\033[35mĐã hết token\033[0m\n");
        }
    }

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $ch = curl_init();
    if (defined('USE_PROXY')) {
        $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();

        if (isset($_proxy['proxy'])) {
            $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
            echo $_proxy['proxy'] . "\n";
            curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
            if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
            }
            curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
            curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
        }
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    $data = json_decode($json, true);

    if (MSC_TOKEN_USE) {
        if (is_numeric($data)) {
            // Xử lý khi token chết: Cập nhật cho nó usetime < 0 rồi cho chạy vòng mới để đổi proxy luôn
            update_token_die($token['id']);
        } else {
            update_token_success($token['id']);
        }
    }
    if (isset($data['contractorsResultAll'])) {
        return $data;
    } elseif (is_array($data) and array_key_exists('contractorsResultAll', $data)) {
        $info['http_code'] = 404;
        return $info;
    } elseif ($reload and $num_run_offer < 5) {
        return geturlpage($_row, 1);
    } elseif ($reload) {
        return geturlpage($_row, 0);
    }
    return [];
}

function getdata($bidding_reoffer, $bidding_detail, $data_id)
{
    global $db, $dbcr, $dm_lvlcnt, $pt_num_map, $date;

    $DM_LHDLCNT = [];
    statusPTLCNT($DM_LHDLCNT);

    $kqcgtt = [];
    $kqcgtt['hinh_thuc_lua_chon'] = $bidding_detail['bidForm'];
    $kqcgtt['type_choose_id'] = get_htdauthau($DM_LHDLCNT[$kqcgtt['hinh_thuc_lua_chon']]['name']);
    $kqcgtt['phuong_thuc_hop_dong'] = empty($bidding_detail['bidMode']) ? '' : $bidding_detail['bidMode'];
    $kqcgtt['gia_goi_thau'] = empty($bidding_detail['bidDetail']['bidPrice']) ? 0 : $bidding_detail['bidDetail']['bidPrice'];
    $kqcgtt['gia_tran'] = isset($bidding_detail['priceInit']) ? number_format($bidding_detail['priceInit'], 0, '', '.') . ' ' . $bidding_detail['bidDetail']['bidPriceUnit'] : '';
    $kqcgtt['buoc_gia'] = isset($bidding_detail['priceStep']) ? number_format($bidding_detail['priceStep'], 0, '', '.') . ' ' . $bidding_detail['bidDetail']['bidPriceUnit'] : '';
    // $kqcgtt['province_id'] = empty($bidding_detail['bidDetail']['bidLocation']) ? 0 : (!empty(array_column($bidding_detail['bidDetail']['bidLocation'], 'provCode')) ? implode(',', array_column($bidding_detail['bidDetail']['bidLocation'], 'provCode')) : 0);
    $kqcgtt['so_tbmt'] = $bidding_detail['notifyNo'] . '-' . $bidding_detail['notifyVersion'];
    $kqcgtt['notify_no'] = $bidding_detail['bidNo'];
    $kqcgtt['id_msc'] = $bidding_detail['id'];
    $kqcgtt['linh_vuc'] = !empty($dm_lvlcnt[$bidding_detail['investField']]) ? (int)$dm_lvlcnt[$bidding_detail['investField']] : 7;
    // $kqcgtt['ngay_dang_tai'] = strtotime($bidding_detail['publicDate']);
    // $kqcgtt['phuong_thuc_num'] = $pt_num_map[strtolower(trim($bidding_detail['bidMode']))];
    // $kqcgtt['pham_vi'] = $bidding_detail['processApply'];
    $kqcgtt['goi_thau'] = empty($bidding_detail['bidName']) ? '' : $bidding_detail['bidName'];
    $kqcgtt['ben_moi_thau'] = empty($bidding_detail['procuringEntityName']) ? '' : $bidding_detail['procuringEntityName'];
    $procuringEntityCode = isset($bidding_detail['procuringEntityCode']) ? $bidding_detail['procuringEntityCode'] : $bidding_detail['investorCode'];
    $kqcgtt['solicitor_id'] = get_solicitor_id($bidding_detail['ben_moi_thau'], $procuringEntityCode);
    $kqcgtt['solicitor_unlink_id'] = 0;
    if (empty($kqcgtt['solicitor_id'])) {
        $kqcgtt['solicitor_unlink_id'] = insert_solicitor_unlink($kqcgtt['ben_moi_thau']);
    }
    $kqcgtt['chu_dau_tu'] = isset($bidding_detail['investorName']) ? $bidding_detail['investorName'] : '';
    // !empty($kqcgtt['chu_dau_tu']) && $kqcgtt['investor_id'] = get_solicitor_id($kqcgtt['chu_dau_tu'], $bidding_detail['investorCode']);
    // empty($kqcgtt['investor_id']) && $kqcgtt['investor_id'] = -1;
    $kqcgtt['get_time'] = NV_CURRENTTIME;

    $contractor_detail = isset($bidding_detail['contractorsResultAll']) ? $bidding_detail['contractorsResultAll'] : [];
    $so_tbmt = $bidding_reoffer['notifyNo'] . '-' . $bidding_detail['notifyVersion'];
    $nha_thau_tham_du = array_unique(array_column($contractor_detail, 'contractorCode'));
    $kqcgtt['so_luong_nha_thau'] = count($nha_thau_tham_du);

    $id_tbmt = $db->query('SELECT id FROM nv4_vi_bidding_row WHERE so_tbmt = ' . $db->quote($kqcgtt['so_tbmt']))->fetchcolumn();
    $kqcgtt['id_tbmt'] = !empty($id_tbmt) ? $id_tbmt : 0;

    $kqcgtt['status'] = $bidding_reoffer['status'];
    $kqcgtt['thoi_gian_thuc_hien'] = $bidding_detail['bidDetail']['cperiod'] . ' ' . $date[$bidding_detail['bidDetail']['cperiodUnit']];
    // $kqcgtt['ten_du_an'] = !empty($bidding_detail['projectName']) ? $bidding_detail['projectName'] : '';
    $kqcgtt['phuong_thuc_hop_dong'] = isset($bidding_detail['contractType']) ? $bidding_detail['contractType'] : '';
    $kqcgtt['nguon_von'] = empty($bidding_detail['bidDetail']['capitalDetail']) ? '' : $bidding_detail['bidDetail']['capitalDetail'];
    $kqcgtt['khlcnt_code'] = $bidding_detail['planNo'];
    // $kqcgtt['khlcnt_title'] = $bidding_detail['planName'];
    $kqcgtt['thoi_diem_bat_dau'] = nv_str2timestamp($bidding_detail['reofferCloseDate']);
    $kqcgtt['thoi_diem_ket_thuc'] = nv_str2timestamp($bidding_detail['reofferOpenDate']);
    $bidding_detail['bidValidityPeriodUnit'] = isset($bidding_detail['bidValidityPeriodUnit']) ? $bidding_detail['bidValidityPeriodUnit'] : 'D';
    $kqcgtt['time_ehsdt'] = $bidding_detail['bidValidityPeriod'] . ' ' . $date[$bidding_detail['bidValidityPeriodUnit']];
    $bidpBidLocationList = isset($bidding_detail['bidDetail']['bidLocation']) ? $bidding_detail['bidDetail']['bidLocation'] : [];
    $kqcgtt['dia_diem_thuc_hien'] = '';
    $kqcgtt['translator'] = 0;
    $array_location = [];
    foreach ($bidpBidLocationList as $k => $v) {
        if (isset($v['wardName'])) {
            $array_location[$k][] = $v['wardName'];
        }
        if (isset($v['districtName'])) {
            $array_location[$k][] = $v['districtName'];
        }
        if (isset($v['provName'])) {
            $array_location[$k][] = $v['provName'];
        }
    }
    if (!empty($array_location)) {
        $kqcgtt['dia_diem_thuc_hien'] = implode('; ', array_map(function ($a) {
            return implode(', ', $a);
        }, $array_location));
    }

    $content = $bidding_detail['notifyNo'] . ' ' . $kqcgtt['goi_thau'] . ' ' . $kqcgtt['ben_moi_thau'] . ' ' . $kqcgtt['chu_dau_tu'] . ' ' . $kqcgtt['notify_no'] . ' ' . $kqcgtt['khlcnt_code'];
    if (!empty($kqcgtt['dia_diem_thuc_hien'])) {
        $content .= ' ' . $kqcgtt['dia_diem_thuc_hien'];
    }
    if (!empty($kqcgtt['nguon_von'])) {
        $content .= ' ' . $kqcgtt['nguon_von'];
    }
    $content = substr($content, 0, 65000);
    $kqcgtt['content_full'] = $content;
    $content = change_alias($content);
    $kqcgtt['content'] = str_replace('-', ' ', $content);

    $data = nv_compound_unicode_recursion($kqcgtt);
    $id = bidding_reoffer($data);
    if ($id != 0) {
        echo ("Insert or Update Reoffer Success With so tbmt:" . $id);
        echo "\n\n";
    }

    // Dữ liệu bidding reoffer result
    $db->query("DELETE FROM nv4_vi_bidding_reoffer_result WHERE so_tbmt = " . $db->quote($so_tbmt));
    if ($kqcgtt['so_luong_nha_thau'] > 0) {
        foreach ($contractor_detail as $k => $v) {
            $detail = [
                'lotno' => $v['lotNo'], //Mã phần lô
                'lotname' => $v['lotName'], //Tên phần lô
                'so_tbmt' => $so_tbmt,
                'org_code' => $v['contractorCode'], // mã định danh
                'ten_nha_thau' => $v['contractorName'],
                'ten_lien_danh' => (!empty($v['ventureName']) ? $v['ventureName'] : ''),
                'gia_du_thau' => (!empty($v['reofferPrice']) ? $v['reofferPrice'] : 0),
                'gia_du_thau_cuoi' => (!empty($v['reofferPriceFinal']) ? $v['reofferPriceFinal'] : 0),
                'thoi_gian_chao_gia_cuoi' => (!empty($v['reofferDate']) ? strtotime($v['reofferDate']) : 0),
                'is_newest' => $v['isNewest'],
                'xep_hang' => $v['times'],
                'updatetime' => NV_CURRENTTIME,
            ];

            // Nếu có tên liên danh thì
            $v['ventureName'] = isset($v['ventureName']) ? trim($v['ventureName']) : '';
            if ($v['ventureName'] != "") {
                $detail['partnership'] = 2;
            } else {
                $detail['partnership'] = 1;
            }
            $id_result = bidding_reoffer_result($detail, $v['ventureName']);
            if ($id_result < 1) {
                echo "\n";
                echo ("No Insert or Update ReofferResult");
            } else {
                echo "\n";
            }
        }
    }

    //Update crawls_kqcgtt
    $dbcr->query('UPDATE `nv23_crawls_kqcgtt` SET url_run=' . NV_CURRENTTIME . ', dauthau_info = ' . $id . ', crawls_info=\'so_luong_nha_thau=' . $kqcgtt['so_luong_nha_thau'] . '\'  WHERE `id` = ' . $data_id);
    echo "Xong TBMT:" . $bidding_reoffer['notifyNo'];
    echo "\n";
}

function send_mail_follow_kqcgtt($bid_id, $so_tbmt, $result_id) {
    global $db;
    try {
        if (empty($result_id)) {
            return;
        }

        $so_tbmt = explode('-', $so_tbmt);
        $follows = $db->query('SELECT * FROM nv4_vi_bidding_follow WHERE bid_code=' . $db->quote($so_tbmt[0]));

        $total_follows = $follows->rowCount();
        echo "Tổng số người theo dõi TBMT " . $so_tbmt[0] . ": " . $total_follows . PHP_EOL;

        while ($follow = $follows->fetch()) {
            $check = $db->query('SELECT count(id) FROM nv4_vi_bidding_bid_id WHERE bid_id=' . $bid_id . ' AND userid=' . $follow['userid'] . ' AND reoffer_result_id = ' . $db->quote($result_id))->fetchColumn();

            if (empty($check)) {
                // Thêm mới thông báo vào bảng bid_id
                $stmt = $db->prepare('INSERT INTO nv4_vi_bidding_bid_id
                    (userid, filter_id, bid_id, reoffer_result_id, addtime, send_status)
                    VALUES (:userid, :filter_id, :bid_id, :reoffer_result_id, :addtime, 0)');

                $stmt->bindParam(':userid', $follow['userid'], PDO::PARAM_INT);
                $stmt->bindValue(':filter_id', 0, PDO::PARAM_INT);
                $stmt->bindParam(':bid_id', $bid_id, PDO::PARAM_INT);
                $stmt->bindParam(':reoffer_result_id', $result_id, PDO::PARAM_INT);
                $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);

                $stmt->execute();
                echo "Thêm mới thông báo vào bảng bid_id: " . $bid_id . " và result_id: " . $result_id . PHP_EOL;
            }
        }
    } catch (Exception $e) {
        print_r('Lỗi gửi mail kết quả chào giá trực tuyến: ' . $e->getMessage());
        trigger_error($e->getMessage());
    }
}

function bidding_reoffer($data)
{
    global $db, $dbcr;
    $_id = $db->query('SELECT id FROM nv4_vi_bidding_reoffer WHERE so_tbmt = ' . $db->quote($data['so_tbmt']))->fetchcolumn();
    $id = 0;
    $action = 'new';

    // TH thêm dữ liệu vào bảng reoffer
    if (empty($_id)) {
        $data['fget_time'] = NV_CURRENTTIME;
        $data['alias'] = nv_clean_alias($data['goi_thau']);
        if (empty($data['alias'])) {
            $data['alias'] = nv_clean_alias($data['so_tbmt']);
        }

        foreach ($data as $key => $value) {
            $arrayKey[] = $key;
            $arrayInsertReoffer[] = $db->quote($value);
        }
        $sqlInsertReoffer = "INSERT INTO `nv4_vi_bidding_reoffer` (" . implode(",", $arrayKey) . ") VALUES ( " . implode(",", $arrayInsertReoffer) . " )";
        $db->query($sqlInsertReoffer);
        $id = $db->lastInsertId();
        $action = 'new';
        echo "Insert nv4_vi_bidding_reoffer success with TBMT:" . $data['so_tbmt'];
        echo ("\n");
    } else {
        $row_old = $db->query('SELECT * FROM `nv4_vi_bidding_reoffer` WHERE id = ' . $db->quote($_id))->fetch();

        // Kiểm tra có thay đổi thực sự không
        $updateFields = [];
        foreach ($data as $key => $value) {
            if (isset($row_old[$key]) && $row_old[$key] != $value) {
                $updateFields[] = $key . "=" . $db->quote($value);
            }
        }

        if (!empty($updateFields)) {
            $data['updatetime'] = NV_CURRENTTIME;
            $updateFields[] = "updatetime=" . $db->quote($data['updatetime']);

            $sql_update = "UPDATE nv4_vi_bidding_reoffer SET " . implode(",", $updateFields) . ' WHERE id = ' . $_id;
            $exc = $db->query($sql_update);

            if ($exc and !empty($row_old['solicitor_unlink_id']) and empty($data['solicitor_unlink_id'])) {
                $db->query("UPDATE " . BID_PREFIX_GLOBAL . "_solicitor_unlink_data SET update_data='0' WHERE id = " . $db->quote($row_old['solicitor_unlink_id']));
            }

            $action = 'update';
            echo "Update nv4_vi_bidding_reoffer success with id:" . $_id;
            echo ("\n");

            $row_new = $db->query('SELECT * FROM nv4_vi_bidding_reoffer WHERE id = ' . $_id)->fetch();
            $status_insert = insert_log_crawls($_id, 'KQCGTT', $row_old, $row_new);
            if ($status_insert > 0) {
                echo ("LOG: KQCGTT - ID: " . $_id . "- OK \n");
            }
        } else {
            echo "No changes detected for KQCGTT ID: " . $_id . "\n";
        }

        $id = $_id;
    }

    // Chỉ gửi thông báo khi insert mới hoặc update có thay đổi thông tin
    if ($id != 0 && ($action == 'new' || ($action == 'update' && !empty($updateFields)))) {
        $bid_info = $db->query('SELECT id FROM nv4_vi_bidding_row WHERE so_tbmt = ' . $db->quote($data['so_tbmt']))->fetch();
        if (!empty($bid_info)) {
            send_mail_follow_kqcgtt($bid_info['id'], $data['so_tbmt'], $id);
        }
    }

    return $id;
}

// luu cac nha thau tham gia dau thau: nv4_vi_bidding_reoffer_result
function bidding_reoffer_result($data, $tenliendanh, $status = 0)
{
    global $db;
    // $status = 1 => thì k update cái update_partnership
    // Chuyển đổi dữ liệu unicode
    $data = nv_compound_unicode_recursion($data);

    $tenliendanh = nv_compound_unicode($tenliendanh);
    $check = 0;
    if ($data['org_code'] != "") {
        $soddkd = get_ma_so_thue($data['org_code'], $data['so_tbmt']);
        $data['so_dkkd'] = $soddkd;
    }

    $arrayInsertResult = [];
    $arrayKey = [];
    foreach ($data as $key => $value) {
        $arrayKey[] = $key;
        $arrayInsertResult[] = $db->quote($value);
    }

    $sqlInsertReofferResult = "INSERT INTO `nv4_vi_bidding_reoffer_result` (" . implode(",", $arrayKey) . ") VALUES ( " . implode(",", $arrayInsertResult) . " )";
    $db->query($sqlInsertReofferResult);
    $id = $db->lastInsertId();
    if ($id != 0) {
        echo "Insert nv4_vi_bidding_reoffer_result success with id:" . $id;
        echo ("\n");
        $check = $id;
    }

    // cập nhật lại trạng thái của nhà thầu để thống kê
    $db->query("UPDATE `" . BUSINESS_PREFIX_GLOBAL . "_info` SET update_data = '0' WHERE code= " . $db->quote($data['so_dkkd']));
    return $check;
}
